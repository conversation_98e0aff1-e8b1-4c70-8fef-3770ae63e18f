import {
  Either2,
  Fv<PERSON><PERSON><PERSON>,
  left,
  NotFoundError,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';
import { mock } from 'jest-mock-extended';

import { DeletePaymentUseCase } from '@/payments/application/DeletePaymentUseCase';
import { GetPaymentsResourcesService } from '@/payments/domain/services/GetPaymentsResourcesService';
import { MotherCreator } from '@tests/stubs/MotherCreator';
import { PaymentMother } from '@tests/stubs/Payments/PaymentsMother';

import type { PassRepository } from '@/passes/passes/domain/contracts/PassRepository';
import type { DeletePaymentDto } from '@/payments/domain/contracts/DeletePaymentDtoContract';
import type { PaymentRepository } from '@/payments/domain/contracts/PaymentRepository';
import type { TicketRepository } from '@/tickets/tickets/domain/contracts/TicketRepository';

describe(`${DeletePaymentUseCase.name}`, () => {
  const paymentRepository = mock<PaymentRepository>();
  const passRepository = mock<PassRepository>();
  const ticketRepository = mock<TicketRepository>();

  const getPaymentsResourcesService = new GetPaymentsResourcesService(
    ticketRepository,
    passRepository,
  );

  const useCase = new DeletePaymentUseCase(paymentRepository, getPaymentsResourcesService);

  const payment = PaymentMother.buildDefault();

  const deletePaymentDto: DeletePaymentDto = {
    paymentId: UniqueEntityID.build(payment.id),
    idx: MotherCreator.random().lorem.slug(16),
  };

  afterEach(async () => {
    jest.clearAllMocks();
  });

  it('should return NotFoundError when payment does not exist', async () => {
    const notFoundError = NotFoundError.build({
      context: DeletePaymentUseCase.name,
      target: 'Payment',
      data: { dto: deletePaymentDto },
    });

    paymentRepository.find.mockResolvedValue(left(notFoundError));

    const useCaseResult = await useCase.execute(deletePaymentDto);

    expect(useCaseResult.isLeft()).toBeTruthy();
    expect(useCaseResult.get()).toBeInstanceOf(NotFoundError);
  });

  it('should successfully delete payment when it exists and return 1', async () => {
    const deletedCount = FvNumber.build(1);

    paymentRepository.find.mockResolvedValue(right(payment));
    paymentRepository.save.mockResolvedValue(Either2.right(deletedCount));

    const useCaseResult = await useCase.execute(deletePaymentDto);

    expect(useCaseResult.isRight()).toBeTruthy();

    const deleteResult = useCaseResult.get() as FvNumber;

    expect(deleteResult.toPrimitive()).toBe(deletedCount.toPrimitive());
  });
});

import {
  Either2,
  EPaymentStates,
  left,
  Maybe,
  NotFoundError,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';
import { mock } from 'jest-mock-extended';

import { GetMicrositeChannelService } from '@/microsite/infrastructure/services/GetMicrositeChannelService';
import { SearchPaymentsUseCase } from '@/payments/application/SearchPaymentsUseCase';
import { GetPaymentsResourcesService } from '@/payments/domain/services/GetPaymentsResourcesService';
import { BillingAddressMother } from '@tests/stubs/billingAddress/BillingAddressMother';
import { EventMother } from '@tests/stubs/event/EventMother';
import { MotherCreator } from '@tests/stubs/MotherCreator';
import { PaginationMother } from '@tests/stubs/PaginationMother';
import { PaymentMother } from '@tests/stubs/Payments/PaymentsMother';
import { TicketMother } from '@tests/stubs/ticket/TicketMother';
import { OrganizationMother } from 'tests/stubs/organization/OrganizationMother';

import type { BillingAddress } from '@/billingAddresses/domain/entities/BillingAddress';
import type { EventRepository } from '@/events/events/domain/contracts/EventRepository';
import type {
  EventEntity,
  Events,
} from '@/events/events/domain/entities/EventEntity';
import type { Organization } from '@/organizations/organizations/domain/entities/Organization';
import type { PassRepository } from '@/passes/passes/domain/contracts/PassRepository';
import type { Pass } from '@/passes/passes/domain/entities/Pass';
import type { PaymentRepository } from '@/payments/domain/contracts/PaymentRepository';
import type { SearchPaymentsDto } from '@/payments/domain/contracts/SearchPaymentsDtoContract';
import type { Payment, SearchPaginatedPaymentsResponse } from '@/payments/domain/entities/Payment';
import type { TicketRepository } from '@/tickets/tickets/domain/contracts/TicketRepository';
import type { Ticket } from '@/tickets/tickets/domain/entities/TicketEntity';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';

describe(`${SearchPaymentsUseCase.name}`, () => {
  const eventRepository = mock<EventRepository>();
  const paymentRepository = mock<PaymentRepository>();
  const ticketRepository = mock<TicketRepository>();
  const passRepository = mock<PassRepository>();

  const getPaymentsResourcesService = new GetPaymentsResourcesService(
    ticketRepository,
    passRepository,
  );

  const useCase = new SearchPaymentsUseCase(
    eventRepository,
    paymentRepository,
    getPaymentsResourcesService,
  );

  const organization = OrganizationMother.buildDefault();
  const organizations = new Map<IdPrimitive, Organization>();

  organizations.set(organization.id, organization);

  const events: Events = new Map<IdPrimitive, EventEntity>();
  const eventsInvalid = new Map<IdPrimitive, EventEntity>();
  const buildEvent = (organizationId: string): EventEntity => EventMother.buildWithCustomData({ organizationId });

  let event = buildEvent(organization.id);

  events.set(event.id, event);

  event = buildEvent(organization.id);
  events.set(event.id, event);

  event = buildEvent(organization.id);
  events.set(event.id, event);

  event = buildEvent(organization.id);
  events.set(event.id, event);

  const eventNotActive = EventMother.buildWithCustomData({ isActive: false });

  eventsInvalid.set(eventNotActive.id, eventNotActive);

  const idxForTicket = MotherCreator.random().lorem.words(2);
  const tickets = new Map<IdPrimitive, Ticket>();

  let ticket = TicketMother.buildWithCustomData({ eventId: event.id, idx: Maybe.some(idxForTicket) });

  tickets.set(ticket.id, ticket);

  ticket = TicketMother.buildWithCustomData({ eventId: event.id, idx: Maybe.some(idxForTicket) });

  tickets.set(ticket.id, ticket);

  const resourceIdsForPayment = [...tickets.keys()];

  const billingAddress = BillingAddressMother.buildWithCustomData({ organizationId: organization.id });
  const billingAddresses = new Map<IdPrimitive, BillingAddress>();

  billingAddresses.set(billingAddress.organizationId, billingAddress);

  const payments = new Map<IdPrimitive, Payment>();
  const paymentsInvalid = new Map<IdPrimitive, Payment>();

  const pendingPayment = PaymentMother.createWithCustomData({
    state: EPaymentStates.PENDING,
    eventId: Maybe.fromValue(event.id),
    resourceIds: resourceIdsForPayment,
  });

  payments.set(pendingPayment.id, pendingPayment);

  const pendingPaymentFromOtherEvent = PaymentMother.createWithCustomData({
    state: EPaymentStates.PENDING,
    eventId: Maybe.fromValue(UniqueEntityID.create().toPrimitive()),
    resourceIds: resourceIdsForPayment,
  });

  payments.set(pendingPaymentFromOtherEvent.id, pendingPaymentFromOtherEvent);

  const pendingPaymentFromNotActiveEvent = PaymentMother.createWithCustomData({
    state: EPaymentStates.PENDING,
    eventId: Maybe.fromValue(eventNotActive.id),
    resourceIds: resourceIdsForPayment,
  });

  paymentsInvalid.set(pendingPaymentFromNotActiveEvent.id, pendingPaymentFromNotActiveEvent);

  const paginationMetadata = PaginationMother.buildMetadata();

  afterEach(async () => {
    jest.clearAllMocks();
  });

  it('should return NotFoundError', async () => {
    const notFound = NotFoundError.build({ context: GetMicrositeChannelService.name, target: 'Slug not found' });

    const request: SearchPaymentsDto = {
      pagination: PaginationMother.buildOption(),
      eventId: UniqueEntityID.create(),
      idx: '',
      status: EPaymentStates.PENDING,
    };

    eventRepository.find.mockResolvedValue(left(notFound));

    const useCaseResult = await useCase.execute(request);

    expect(useCaseResult.isLeft()).toBeTruthy();
    expect(useCaseResult.get()).toBeInstanceOf(NotFoundError);
  });

  it('should return NotFoundError when event is not active', async () => {
    const request: SearchPaymentsDto = {
      pagination: PaginationMother.buildOption(),
      eventId: UniqueEntityID.create(),
      idx: '',
      status: EPaymentStates.PENDING,
    };

    eventRepository.find.mockResolvedValue(right(eventNotActive));

    const useCaseResult = await useCase.execute(request);

    expect(useCaseResult.isLeft()).toBeTruthy();
    expect(useCaseResult.get()).toBeInstanceOf(NotFoundError);
  });

  it('should return NotFoundError when no payment is found', async () => {
    const request: SearchPaymentsDto = {
      pagination: PaginationMother.buildOption(),
      eventId: UniqueEntityID.create(),
      idx: '',
      status: EPaymentStates.PENDING,
    };

    eventRepository.find.mockResolvedValue(right(event));
    paymentRepository.search.mockResolvedValue(Either2.left(NotFoundError.build({ context: 'Payment' })));

    const useCaseResult = await useCase.execute(request);

    expect(useCaseResult.isLeft()).toBeTruthy();
    expect(useCaseResult.get()).toBeInstanceOf(NotFoundError);
  });

  it('should return all ok', async () => {
    const request: SearchPaymentsDto = {
      pagination: PaginationMother.buildOption(),
      eventId: UniqueEntityID.create(),
      idx: '',
      status: EPaymentStates.PENDING,
    };

    eventRepository.find.mockResolvedValue(right(event));

    paymentRepository.search.mockResolvedValue(Either2.right({
      payments,
      pagination: paginationMetadata,
    }));

    const passes = new Map<IdPrimitive, Pass>();

    passRepository.search.mockResolvedValue(right(passes));

    ticketRepository.search.mockResolvedValue(right({ tickets, pagination: paginationMetadata }));

    const useCaseResult = await useCase.execute(request);

    expect(useCaseResult.isRight()).toBeTruthy();

    const { paymentsWithTickets } = useCaseResult.get() as SearchPaginatedPaymentsResponse;

    expect(paymentsWithTickets.size).toBe(2);

    for (const { payment } of [...paymentsWithTickets.values()]) {
      expect(payment.resourceIds).toEqual(resourceIdsForPayment);
    }
  });
});

import {
  left, NotFoundError, right,
} from '@discocil/fv-domain-library/domain';

import { CacheRepository } from '@/cross-cutting/infrastructure/database/repositories/CacheRepository';
import { PassJsonMapper } from '@/passes/passes/domain/mappers/PassJsonMapper';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';
import type { PassRepository } from '@/passes/passes/domain/contracts/PassRepository';
import type {
  Pass,
  PassEither,
  Passes,
  PassesEither,
} from '@/passes/passes/domain/entities/Pass';
import type { PassJsonPrimitives } from '@/passes/passes/domain/mappers/PassJsonMapper';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';

export class PassCacheRepository extends CacheRepository implements PassRepository {
  constructor(
    private readonly cacheHandler: ICacheRepository,
    private readonly cacheConfig: CacheConfig,
    private readonly otherRepository?: PassRepository,
  ) {
    super();
  }

  protected getConfig(): CacheConfig {
    return this.cacheConfig;
  }

  protected getHandler(): ICacheRepository {
    return this.cacheHandler;
  }

  async count(criteria: Criteria): Promise<number> {
    if (!this.otherRepository) {
      return 0;
    }

    return this.otherRepository.count(criteria);
  }

  async find(criteria: Criteria): Promise<PassEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: PassJsonPrimitives = JSON.parse(cacheHit);
      const entityOrError = PassJsonMapper.toEntity(jsonPrimitives);

      if (entityOrError.isRight()) {
        return entityOrError;
      }
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.find(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      await this.cacheHandler.set(
        cacheKey,
        PassJsonMapper.toJson(repositoryResult.value),
        this.getConfig().ttl,
      );
    }

    return repositoryResult;
  }

  async search(criteria: Criteria): Promise<PassesEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: PassJsonPrimitives[] = JSON.parse(cacheHit);
      const passes: Passes = new Map<IdPrimitive, Pass>();

      for (const _primitive of jsonPrimitives) {
        const entityOrError = PassJsonMapper.toEntity(_primitive);

        if (entityOrError.isLeft()) {
          continue;
        }

        passes.set(_primitive.id, entityOrError.value);
      }

      return right(passes);
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.search(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      const passes = repositoryResult.value;
      const passesLength = passes.size;
      let passesCacheIndex = 0;
      const jsonPasses = new Array<PassJsonPrimitives>(passesLength);

      if (passesLength > 0) {
        for (const _pass of passes.values()) {
          jsonPasses[passesCacheIndex] = PassJsonMapper.toJson(_pass);

          passesCacheIndex++;
        }
      }

      await this.cacheHandler.set(cacheKey, jsonPasses, this.getConfig().ttl);
    }

    return repositoryResult;
  }

  async save(pass: Pass): Promise<void> {
    if (!this.otherRepository) {
      return;
    }

    await this.otherRepository.save(pass);
  }

  async saveMany(passes: Passes): Promise<void> {
    if (!this.otherRepository) {
      return;
    }

    await this.otherRepository.saveMany(passes);
  }
}

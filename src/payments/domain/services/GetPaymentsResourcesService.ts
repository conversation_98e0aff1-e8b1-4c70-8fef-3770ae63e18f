import { UniqueEntityID } from '@discocil/fv-domain-library/domain';

import { PassCriteriaMother } from '@/passes/passes/domain/filters/PassCriteriaMother';
import { TicketCriteriaMother } from '@/tickets/tickets/domain/filters/TicketCriteriaMother';

import type { PassRepository } from '@/passes/passes/domain/contracts/PassRepository';
import type { Passes } from '@/passes/passes/domain/entities/Pass';
import type { TicketRepository } from '@/tickets/tickets/domain/contracts/TicketRepository';
import type { Tickets } from '@/tickets/tickets/domain/entities/TicketEntity';
import type { Payments } from '../entities/Payment';

export type GetPaymentResourcesResponse = {
  tickets: Tickets;
  passes: Passes;
};

export class GetPaymentsResourcesService {
  constructor(
    private readonly ticketRepository: TicketRepository,
    private readonly passRepository: PassRepository,
  ) { }

  async execute(payments: Payments): Promise<GetPaymentResourcesResponse> {
    let tickets: Tickets = new Map();
    let passes: Passes = new Map();

    const ticketIds: UniqueEntityID[] = [];
    const passIds: UniqueEntityID[] = [];

    for (const _payment of payments.values()) {
      if (_payment.isForTicket()) {
        ticketIds.push(..._payment.resourceIds.map(id => UniqueEntityID.build(id)));
      }

      if (_payment.isForPass()) {
        passIds.push(..._payment.resourceIds.map(id => UniqueEntityID.build(id)));
      }
    }

    const ticketsCriteria = TicketCriteriaMother.idsToMatch(ticketIds);
    const passCriteria = PassCriteriaMother.idsToMatch(passIds);

    const [ticketsPromiseResult, passPromiseResult] = await Promise.allSettled([
      this.ticketRepository.search(ticketsCriteria),
      this.passRepository.search(passCriteria),
    ]);

    if (ticketsPromiseResult.status === 'fulfilled') {
      const ticketsResult = ticketsPromiseResult.value;

      if (ticketsResult.isRight()) {
        tickets = ticketsResult.value.tickets;
      }
    }

    if (passPromiseResult.status === 'fulfilled') {
      const passResult = passPromiseResult.value;

      if (passResult.isRight()) {
        passes = passResult.value;
      }
    }

    return {
      tickets,
      passes,
    };
  }
}

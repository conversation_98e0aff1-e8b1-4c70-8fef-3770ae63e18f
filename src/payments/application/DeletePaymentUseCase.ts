import {
  contextualizeError,
  IdPrimitive,
  type UseCase,
} from '@discocil/fv-domain-library';
import { Either2, NotFoundError } from '@discocil/fv-domain-library/domain';

import { Passes } from '@/passes/passes/domain/entities/Pass';
import { Tickets } from '@/tickets/tickets/domain/entities/TicketEntity';

import { PaymentRepository } from '../domain/contracts/PaymentRepository';
import { Payment, UpdatePaymentEither } from '../domain/entities/Payment';
import { PaymentCriteriaMother } from '../domain/filters/PaymentCriteriaMother';
import { GetPaymentsResourcesService } from '../domain/services/GetPaymentsResourcesService';

import type { DeletePaymentDto } from '../domain/contracts/DeletePaymentDtoContract';

export class DeletePaymentUseCase implements UseCase<DeletePaymentDto, Promise<UpdatePaymentEither>> {
  constructor(
    private readonly paymentRepository: PaymentRepository,
    private readonly getPaymentResourcesService: GetPaymentsResourcesService,
  ) { }

  @contextualizeError()
  async execute(dto: DeletePaymentDto): Promise<UpdatePaymentEither> {
    const { paymentId, idx } = dto;

    const deleteCriteria = PaymentCriteriaMother.deleteToMatch(paymentId);

    const paymentOrError = await this.paymentRepository.find(deleteCriteria);

    if (paymentOrError.isLeft()) {
      return paymentOrError.value;
    }

    const payment = paymentOrError.value;

    const paymentsForService = new Map<IdPrimitive, Payment>();

    paymentsForService.set(payment.id, payment);

    const { tickets, passes } = await this.getPaymentResourcesService.execute(paymentsForService);

    const isActionAllowed = this.checkAllTicketsIdx(tickets, idx)
      && this.checkAllPassesIdx(passes, idx);

    if (!isActionAllowed) {
      return Either2.left(NotFoundError.build({
        context: this.constructor.name,
        target: 'Payment',
        data: { dto },
      }));
    }

    payment.setExpiresAtNow();

    return await this.paymentRepository.save(payment);
  }

  private checkAllTicketsIdx(resources: Tickets, idx: string): boolean {
    if (resources.size === 0) {
      return true;
    }

    for (const _resource of resources.values()) {
      if (_resource.idx.isDefined() && _resource.idx.get() !== idx) {
        return false;
      }
    }

    return true;
  }

  private checkAllPassesIdx(resources: Passes, idx: string): boolean {
    if (resources.size === 0) {
      return true;
    }

    for (const _resource of resources.values()) {
      if (_resource.idx !== idx) {
        return false;
      }
    }

    return true;
  }
}

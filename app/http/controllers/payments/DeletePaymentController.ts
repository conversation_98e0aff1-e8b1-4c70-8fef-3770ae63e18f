import { singleton } from 'tsyringe';

import { DeletePaymentUseCase } from '@/payments/application/DeletePaymentUseCase';
import { HTTP_CODES } from '@app/http/HttpCodes';

import { parseDeletePaymentRequest } from './requests/ParseDeletePaymentRequest';

import type { DeletePaymentReply, DeletePaymentRequest } from '@app/http/@types/cli-api/payments/deletePayment/schema';

@singleton()
export class DeletePaymentController {
  constructor(private readonly useCase: DeletePaymentUseCase) { }

  async handler(request: DeletePaymentRequest, reply: DeletePaymentReply): Promise<void> {
    const dto = parseDeletePaymentRequest(request);

    await this.useCase.execute(dto);

    return reply.code(HTTP_CODES.NO_CONTENT_204).send();
  }
}

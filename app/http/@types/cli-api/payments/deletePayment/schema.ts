import { Type } from '@sinclair/typebox';

import type { FastifyReplyTypebox, FastifyRequestTypebox } from '../../cross-cutting/schema';

const paramsSchema = Type.Object({ id: Type.String() });

const querySchema = Type.Object({ idx: Type.String() });

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '204': Type.Object({}),
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const deletePaymentSchema = {
  params: paramsSchema,
  querystring: querySchema,
  response: responseSchema,
};

type Schema = typeof deletePaymentSchema;

export type DeletePaymentRequest = FastifyRequestTypebox<Schema>;
export type DeletePaymentReply = FastifyReplyTypebox<Schema>;
